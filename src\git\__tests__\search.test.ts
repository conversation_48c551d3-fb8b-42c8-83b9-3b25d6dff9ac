import * as assert from 'assert';
import type { SearchQuery } from '../../constants.search';
import type { GitUser } from '../models/user';
import { parseSearchQuery, parseSearchQueryCommand } from '../search';

suite('Search Query Parsing', () => {
	const mockUser: GitUser = { name: 'Test User', email: '<EMAIL>' };

	suite('parseSearchQuery', () => {
		test('should parse since: operator', () => {
			const query: SearchQuery = {
				query: 'since:"2023-01-01" message:"test commit"',
				matchCase: false,
				matchAll: false,
				matchRegex: false,
			};

			const result = parseSearchQuery(query);

			assert.ok(result.has('since:'));
			assert.ok(result.get('since:')?.has('"2023-01-01"'));
			assert.ok(result.has('message:'));
			assert.ok(result.get('message:')?.has('"test commit"'));
		});

		test('should parse until: operator', () => {
			const query: SearchQuery = {
				query: 'until:"2023-12-31" author:@me',
				matchCase: false,
				matchAll: false,
				matchRegex: false,
			};

			const result = parseSearchQuery(query);

			assert.ok(result.has('until:'));
			assert.ok(result.get('until:')?.has('"2023-12-31"'));
			assert.ok(result.has('author:'));
			assert.ok(result.get('author:')?.has('@me'));
		});

		test('should parse both since: and until: operators', () => {
			const query: SearchQuery = {
				query: 'since:"2023-01-01" until:"2023-12-31" message:"feature"',
				matchCase: false,
				matchAll: false,
				matchRegex: false,
			};

			const result = parseSearchQuery(query);

			assert.ok(result.has('since:'));
			assert.ok(result.get('since:')?.has('"2023-01-01"'));
			assert.ok(result.has('until:'));
			assert.ok(result.get('until:')?.has('"2023-12-31"'));
			assert.ok(result.has('message:'));
			assert.ok(result.get('message:')?.has('"feature"'));
		});

		test('should parse unquoted date values', () => {
			const query: SearchQuery = {
				query: 'since:2023-01-01 until:2023-12-31',
				matchCase: false,
				matchAll: false,
				matchRegex: false,
			};

			const result = parseSearchQuery(query);

			assert.ok(result.has('since:'));
			assert.ok(result.get('since:')?.has('2023-01-01'));
			assert.ok(result.has('until:'));
			assert.ok(result.get('until:')?.has('2023-12-31'));
		});
	});

	describe('parseSearchQueryCommand', () => {
		it('should generate --since git argument', () => {
			const query: SearchQuery = {
				query: 'since:"2023-01-01" message:"test"',
				matchCase: false,
				matchAll: false,
				matchRegex: false,
			};

			const result = parseSearchQueryCommand(query, mockUser);

			assert.ok(result.args.includes('--since=2023-01-01'));
			assert.ok(result.args.includes('--grep=test'));
		});

		it('should generate --until git argument', () => {
			const query: SearchQuery = {
				query: 'until:"2023-12-31" author:@me',
				matchCase: false,
				matchAll: false,
				matchRegex: false,
			};

			const result = parseSearchQueryCommand(query, mockUser);

			assert.ok(result.args.includes('--until=2023-12-31'));
			assert.ok(result.args.includes('--author=Test User'));
		});

		it('should generate both --since and --until git arguments', () => {
			const query: SearchQuery = {
				query: 'since:"2023-01-01" until:"2023-12-31" message:"feature"',
				matchCase: false,
				matchAll: false,
				matchRegex: false,
			};

			const result = parseSearchQueryCommand(query, mockUser);

			assert.ok(result.args.includes('--since=2023-01-01'));
			assert.ok(result.args.includes('--until=2023-12-31'));
			assert.ok(result.args.includes('--grep=feature'));
		});

		it('should handle relative date formats', () => {
			const query: SearchQuery = {
				query: 'since:"1 week ago" until:"yesterday"',
				matchCase: false,
				matchAll: false,
				matchRegex: false,
			};

			const result = parseSearchQueryCommand(query, mockUser);

			assert.ok(result.args.includes('--since=1 week ago'));
			assert.ok(result.args.includes('--until=yesterday'));
		});

		it('should handle unquoted date values', () => {
			const query: SearchQuery = {
				query: 'since:2023-01-01 until:2023-12-31',
				matchCase: false,
				matchAll: false,
				matchRegex: false,
			};

			const result = parseSearchQueryCommand(query, mockUser);

			assert.ok(result.args.includes('--since=2023-01-01'));
			assert.ok(result.args.includes('--until=2023-12-31'));
		});
	});
});
